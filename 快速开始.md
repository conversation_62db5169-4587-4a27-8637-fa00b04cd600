# 🚀 快速开始指南

## 第一步：安装依赖

### 方法一：使用安装脚本（推荐）
```bash
python install_dependencies.py
```

### 方法二：手动安装
```bash
pip install google-generativeai
```

## 第二步：获取API Key

1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 登录Google账号
3. 点击 "Get API Key" 创建新的API Key
4. 复制生成的API Key

## 第三步：配置API Key

编辑 `config.json` 文件，将你的API Key填入：

```json
{
  "api_keys": [
    "你的第一个Gemini API Key",
    "你的第二个Gemini API Key（可选）", 
    "你的第三个Gemini API Key（可选）"
  ],
  "start_date": "2025-07-05",
  "end_date": "2025-07-15",
  "versions_per_date": 3
}
```

**💡 提示：**
- 可以配置多个API Key实现轮询，提高稳定性
- 至少需要配置一个有效的API Key
- 其他配置项可以保持默认值

## 第四步：运行程序

### Windows用户
双击运行 `run.bat` 文件

### 或者使用命令行
```bash
python generate_gemini_content.py
```

## 第五步：查看结果

程序运行完成后，会在对应日期文件夹中生成内容：

```
20250705/
├── 1.html  # 第一个版本的HTML图文
├── 1.txt   # 第一个版本的视频文案
├── 2.html  # 第二个版本的HTML图文
├── 2.txt   # 第二个版本的视频文案
├── 3.html  # 第三个版本的HTML图文
└── 3.txt   # 第三个版本的视频文案
```

## 🔧 常见问题

### Q: API Key无效怎么办？
A: 检查以下几点：
- API Key是否正确复制（没有多余空格）
- API Key是否已激活
- Google账号是否有API使用权限

### Q: 程序运行很慢怎么办？
A: 这是正常现象，因为：
- AI生成内容需要时间
- 程序内置了请求间隔避免触发限制
- 可以在config.json中调整`request_delay`参数

### Q: 程序中断了怎么办？
A: 不用担心！程序支持断点续跑：
- 重新运行程序即可
- 已生成的内容不会重复生成
- 程序会从中断处继续执行

### Q: 想修改生成的日期范围怎么办？
A: 编辑config.json文件：
```json
{
  "start_date": "2025-07-01",  # 开始日期
  "end_date": "2025-07-31",    # 结束日期
  "versions_per_date": 3       # 每日版本数
}
```

## 📊 监控进度

- **实时进度**: 查看控制台输出
- **详细日志**: 查看 `gemini_content_generator.log`
- **进度文件**: 查看 `progress.json`

## 🎯 高级用法

### 自定义prompt
编辑 `prompt.md` 文件来自定义AI生成的内容风格和要求

### 批量处理大量日期
程序支持处理大量日期，建议：
- 配置多个API Key
- 适当增加`request_delay`
- 定期检查API配额使用情况

### 错误恢复
如果遇到错误：
1. 查看日志文件了解具体错误
2. 检查网络连接和API配额
3. 重新运行程序（支持断点续跑）

## 📞 技术支持

如果遇到问题：
1. 查看 `README.md` 详细文档
2. 运行 `python test_generator.py` 进行功能测试
3. 检查 `gemini_content_generator.log` 日志文件

---

🎉 **恭喜！你已经成功配置了Gemini批量内容生成器！**

现在可以开始批量生成高质量的AI内容了！
