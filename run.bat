@echo off
chcp 65001 >nul
title Gemini内容生成器

echo.
echo ========================================
echo    Gemini 批量内容生成器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

REM 检查配置文件
if not exist "config.json" (
    echo.
    echo ❌ 错误: 未找到配置文件 config.json
    echo 请先运行安装脚本或手动创建配置文件
    pause
    exit /b 1
)

REM 检查prompt文件
if not exist "prompt.md" (
    echo.
    echo ❌ 错误: 未找到prompt文件 prompt.md
    pause
    exit /b 1
)

echo.
echo 📋 检查配置文件...

REM 简单检查API Key是否配置
findstr /C:"YOUR_GEMINI_API_KEY" config.json >nul
if not errorlevel 1 (
    echo.
    echo ⚠️ 警告: 检测到默认API Key配置
    echo 请编辑 config.json 文件，添加真实的Gemini API Key
    echo.
    set /p choice="是否继续运行? (y/N): "
    if /i not "%choice%"=="y" (
        echo 已取消运行
        pause
        exit /b 0
    )
)

echo.
echo 🚀 启动内容生成器...
echo.

REM 运行主程序
python generate_gemini_content.py

echo.
echo ========================================
echo 程序执行完成
echo ========================================
echo.
echo 📁 生成的内容保存在对应日期文件夹中
echo 📄 详细日志请查看: gemini_content_generator.log
echo 📊 进度信息请查看: progress.json
echo.

pause
