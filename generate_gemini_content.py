import os
import json
import time
import logging
from datetime import datetime, timedelta
from typing import List, Tuple, Optional
import google.generativeai as genai
from google.generativeai.types import GenerationConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gemini_content_generator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GeminiContentGenerator:
    def __init__(self, config_file: str = 'config.json'):
        """初始化内容生成器"""
        self.config = self.load_config(config_file)
        self.api_keys = self.config.get('api_keys', [])
        self.current_key_index = 0
        self.failed_keys = set()
        self.prompt_content = self.read_prompt(self.config.get('prompt_path', 'prompt.md'))
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 5)

        # 生成配置
        self.generation_config = GenerationConfig(
            temperature=0.9,
            top_p=0.95,
            top_k=40,
            max_output_tokens=8192,
        )

        logger.info(f"初始化完成，共加载 {len(self.api_keys)} 个API Key")

    def load_config(self, config_file: str) -> dict:
        """加载配置文件"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认配置文件
                default_config = {
                    "api_keys": [
                        "YOUR_GEMINI_API_KEY_1",
                        "YOUR_GEMINI_API_KEY_2",
                        "YOUR_GEMINI_API_KEY_3"
                    ],
                    "prompt_path": "prompt.md",
                    "start_date": "2025-07-05",
                    "end_date": "2025-07-15",
                    "versions_per_date": 3,
                    "max_retries": 3,
                    "retry_delay": 5,
                    "request_delay": 2
                }
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=False, indent=2)
                logger.warning(f"已创建默认配置文件 {config_file}，请编辑API Keys后重新运行")
                return default_config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}

    def read_prompt(self, prompt_path: str) -> str:
        """读取prompt内容"""
        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.info(f"成功读取prompt文件: {prompt_path}")
                return content
        except Exception as e:
            logger.error(f"读取prompt文件失败: {e}")
            return ""

    def get_next_api_key(self) -> Optional[str]:
        """获取下一个可用的API Key"""
        if len(self.failed_keys) >= len(self.api_keys):
            logger.error("所有API Key都已失效")
            return None

        attempts = 0
        while attempts < len(self.api_keys):
            key = self.api_keys[self.current_key_index]
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)

            if key not in self.failed_keys:
                return key

            attempts += 1

        return None

    def mark_key_failed(self, api_key: str):
        """标记API Key为失效"""
        self.failed_keys.add(api_key)
        logger.warning(f"API Key已标记为失效: {api_key[:10]}...")

    def save_content(self, date_str: str, idx: int, html: str, txt: str) -> bool:
        """保存生成的内容"""
        try:
            folder = os.path.join(os.getcwd(), date_str)
            os.makedirs(folder, exist_ok=True)

            # 保存HTML文件
            html_path = os.path.join(folder, f"{idx}.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html)

            # 保存TXT文件
            txt_path = os.path.join(folder, f"{idx}.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(txt)

            logger.info(f"内容已保存: {date_str}/{idx}.html, {date_str}/{idx}.txt")
            return True
        except Exception as e:
            logger.error(f"保存内容失败: {e}")
            return False

    def parse_ai_response(self, response_text: str) -> Tuple[str, str]:
        """解析AI响应，分离HTML和TXT内容"""
        try:
            html, txt = '', ''

            # 尝试按标准格式解析
            if '[HTML]' in response_text and '[TXT]' in response_text:
                parts = response_text.split('[HTML]')
                if len(parts) > 1:
                    html_part = parts[1]
                    if '[TXT]' in html_part:
                        html_txt_parts = html_part.split('[TXT]')
                        html = html_txt_parts[0].strip()
                        if len(html_txt_parts) > 1:
                            txt = html_txt_parts[1].strip()

            # 如果解析失败，尝试其他格式
            if not html and not txt:
                # 尝试寻找HTML标签
                if '<!DOCTYPE html>' in response_text or '<html' in response_text:
                    # 找到HTML部分
                    html_start = max(
                        response_text.find('<!DOCTYPE html>'),
                        response_text.find('<html')
                    )
                    if html_start != -1:
                        # 寻找HTML结束
                        html_end = response_text.find('</html>', html_start)
                        if html_end != -1:
                            html = response_text[html_start:html_end + 7].strip()
                            # 剩余部分作为TXT
                            remaining = response_text[html_end + 7:].strip()
                            if remaining:
                                txt = remaining
                        else:
                            html = response_text[html_start:].strip()

            # 兜底处理
            if not html and not txt:
                html = response_text.strip()
                logger.warning("无法解析AI响应格式，将全部内容作为HTML保存")

            return html, txt

        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return response_text.strip(), ""

    def call_gemini(self, prompt: str, date_str: str, idx: int) -> Tuple[Optional[str], Optional[str]]:
        """调用Gemini API生成内容"""
        for attempt in range(self.max_retries):
            api_key = self.get_next_api_key()
            if not api_key:
                logger.error("没有可用的API Key")
                return None, None

            try:
                # 配置API Key
                genai.configure(api_key=api_key)
                model = genai.GenerativeModel('gemini-2.5-pro')

                # 构建用户输入
                user_input = f"""请严格按照以下prompt要求，针对日期{date_str}，生成第{idx}个不同风格的故事。

输出格式要求：
[HTML]
(完整的HTML代码)
[TXT]
(视频文案内容)

目标日期：{date_str}
版本编号：第{idx}个版本

Prompt内容如下：
{prompt}"""

                logger.info(f"正在调用Gemini API - 日期: {date_str}, 版本: {idx}, 尝试: {attempt + 1}")

                # 调用API
                response = model.generate_content(
                    user_input,
                    generation_config=self.generation_config
                )

                if response and response.text:
                    html, txt = self.parse_ai_response(response.text)
                    logger.info(f"API调用成功 - 日期: {date_str}, 版本: {idx}")

                    # 添加请求间隔
                    time.sleep(self.config.get('request_delay', 2))
                    return html, txt
                else:
                    logger.warning(f"API返回空响应 - 日期: {date_str}, 版本: {idx}")

            except Exception as e:
                error_msg = str(e).lower()
                logger.error(f"API调用失败 - 日期: {date_str}, 版本: {idx}, 错误: {e}")

                # 检查是否是API Key相关错误
                if any(keyword in error_msg for keyword in ['api key', 'authentication', 'unauthorized', 'invalid key']):
                    self.mark_key_failed(api_key)
                    logger.warning(f"API Key失效，切换到下一个Key")
                    continue

                # 检查是否是配额或速率限制错误
                if any(keyword in error_msg for keyword in ['quota', 'rate limit', 'too many requests']):
                    logger.warning(f"遇到配额/速率限制，等待 {self.retry_delay} 秒后重试")
                    time.sleep(self.retry_delay)
                    continue

                # 其他错误，等待后重试
                if attempt < self.max_retries - 1:
                    logger.warning(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"达到最大重试次数，跳过此任务")

        return None, None

    def check_existing_content(self, date_str: str, idx: int) -> bool:
        """检查内容是否已存在（用于断点续跑）"""
        folder = os.path.join(os.getcwd(), date_str)
        html_path = os.path.join(folder, f"{idx}.html")
        txt_path = os.path.join(folder, f"{idx}.txt")

        return os.path.exists(html_path) and os.path.exists(txt_path)

    def generate_date_range(self, start_date: str, end_date: str) -> List[str]:
        """生成日期范围"""
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')

            dates = []
            current = start
            while current <= end:
                dates.append(current.strftime('%Y%m%d'))
                current += timedelta(days=1)

            return dates
        except Exception as e:
            logger.error(f"生成日期范围失败: {e}")
            return []

    def save_progress(self, completed_tasks: List[dict]):
        """保存进度信息"""
        try:
            progress_file = 'progress.json'
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'completed_tasks': completed_tasks,
                    'last_update': datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存进度失败: {e}")

    def load_progress(self) -> List[dict]:
        """加载进度信息"""
        try:
            progress_file = 'progress.json'
            if os.path.exists(progress_file):
                with open(progress_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('completed_tasks', [])
        except Exception as e:
            logger.error(f"加载进度失败: {e}")
        return []

    def run(self):
        """运行主程序"""
        logger.info("开始批量内容生成任务")

        # 检查配置
        if not self.api_keys or self.api_keys[0] == "YOUR_GEMINI_API_KEY_1":
            logger.error("请先在config.json中配置有效的API Keys")
            return

        if not self.prompt_content:
            logger.error("无法读取prompt内容")
            return

        # 生成日期范围
        start_date = self.config.get('start_date', '2025-07-05')
        end_date = self.config.get('end_date', '2025-07-15')
        versions_per_date = self.config.get('versions_per_date', 3)

        dates = self.generate_date_range(start_date, end_date)
        if not dates:
            logger.error("无法生成日期范围")
            return

        # 加载进度
        completed_tasks = self.load_progress()
        completed_set = {(task['date'], task['version']) for task in completed_tasks}

        total_tasks = len(dates) * versions_per_date
        completed_count = len(completed_tasks)

        logger.info(f"任务概览: 日期范围 {start_date} 到 {end_date}, 每日 {versions_per_date} 个版本")
        logger.info(f"总任务数: {total_tasks}, 已完成: {completed_count}, 剩余: {total_tasks - completed_count}")

        # 执行任务
        for date_str in dates:
            logger.info(f"处理日期: {date_str}")

            for idx in range(1, versions_per_date + 1):
                # 检查是否已完成
                if (date_str, idx) in completed_set:
                    logger.info(f"  版本 {idx} 已存在，跳过")
                    continue

                # 检查文件是否已存在
                if self.check_existing_content(date_str, idx):
                    logger.info(f"  版本 {idx} 文件已存在，标记为完成")
                    completed_tasks.append({
                        'date': date_str,
                        'version': idx,
                        'timestamp': datetime.now().isoformat()
                    })
                    continue

                logger.info(f"  生成版本 {idx}...")

                # 调用API生成内容
                html, txt = self.call_gemini(self.prompt_content, date_str, idx)

                if html is not None and txt is not None:
                    # 保存内容
                    if self.save_content(date_str, idx, html, txt):
                        completed_tasks.append({
                            'date': date_str,
                            'version': idx,
                            'timestamp': datetime.now().isoformat()
                        })
                        logger.info(f"  版本 {idx} 生成完成")
                    else:
                        logger.error(f"  版本 {idx} 保存失败")
                else:
                    logger.error(f"  版本 {idx} 生成失败")

                # 保存进度
                self.save_progress(completed_tasks)

        logger.info("批量内容生成任务完成")
        logger.info(f"最终完成任务数: {len(completed_tasks)}/{total_tasks}")


def main():
    """主函数"""
    try:
        generator = GeminiContentGenerator()
        generator.run()
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")


if __name__ == '__main__':
    main()
