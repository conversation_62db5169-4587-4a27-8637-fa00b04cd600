<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNA双螺旋结构：基因暴富梦之始</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
        }
        .card {
            width: 375px;
            height: 667px;
            position: relative;
            padding: 40px;
            margin: 20px;
            border-radius: 20px;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .page-1 {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        }
        .page-2 {
            background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
        }
        .page-3 {
            background: linear-gradient(135deg, #fd79a8 0%, #8ec5fc 100%);
        }
        .page-4 {
            background: linear-gradient(135deg, #e84393 0%, #e0c3fc 100%);
        }
        .page-5 {
            background: linear-gradient(135deg, #8ec5fc 0%, #fd79a8 100%);
        }
        .emoji {
            font-size: 72px;
            margin-bottom: 20px;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        .subtitle {
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }
        .text {
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
        }
        .tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 15px;
            margin: 5px;
            font-size: 16px;
        }
        .watermark {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 16px;
            opacity: 0.8;
        }
        .history-title {
            font-size: 24px;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- 封面卡 -->
    <div class="card page-1">
        <div class="emoji">🧬</div>
        <div class="title">基因检测：祖宗之忧</div>
        <div class="subtitle">1975年7月5日</div>
        <div class="text">DNA测序技术：最早的基因暴富梦想家</div>
        <div class="watermark">@史今番</div>
    </div>

    <!-- 历史背景卡 -->
    <div class="card page-2">
        <div class="history-title">历史上的今天</div>
        <div class="emoji">🔬</div>
        <div class="text">1975年7月5日<br>Frederick Sanger团队<br>发表DNA测序方法<br>开启基因检测时代</div>
        <div class="watermark">@史今番</div>
    </div>

    <!-- 痛点卡 -->
    <div class="card page-3">
        <div class="emoji">💰</div>
        <div class="title">基因暴富梦</div>
        <div class="text">测一测基因<br>看看有没有富贵命<br>结果发现：<br>富贵基因≠富贵命</div>
        <div class="watermark">@史今番</div>
    </div>

    <!-- 释义卡 -->
    <div class="card page-4">
        <div class="emoji">📖</div>
        <div class="title">史今番辞典</div>
        <div class="text">DNA测序[dí ēn āi cè xù]<br>释义：一种让你知道自己<br>到底是什么命的科技<br>但可能会让你更焦虑</div>
        <div class="watermark">@史今番</div>
    </div>

    <!-- 互动卡 -->
    <div class="card page-5">
        <div class="emoji">💭</div>
        <div class="title">造句时间</div>
        <div class="text">模版：别测了，你的DNA里<br>只写着[    ]<br><br>评论区填空，玩梗不完</div>
        <div class="tag">#基因暴富梦</div>
        <div class="tag">#打工基因说</div>
        <div class="watermark">@史今番</div>
    </div>
</body>
</html>
