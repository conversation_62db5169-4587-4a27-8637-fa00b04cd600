<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>史今番：万物复刻史祖</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* 基础样式和重置 */
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #1a1a1a;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
            padding: 20px 0;
        }

        /* 卡片容器通用样式 */
        .card {
            width: 375px;
            height: 667px;
            border-radius: 20px;
            color: white;
            padding: 30px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            overflow: hidden; /* 确保水印不会溢出 */
        }

        /* 品牌水印 */
        .card::after {
            content: '@史今番';
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.6);
        }

        /* 字体规范 */
        .main-emoji {
            font-size: 70px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .subtitle {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .body-text {
            font-size: 16px;
            line-height: 1.6;
        }
        
        .history-header {
            font-size: 26px;
            font-weight: 700;
            padding: 8px 16px;
            border: 2px solid white;
            border-radius: 12px;
            margin-bottom: 40px;
        }
        
        .icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        /* 卡片工具箱特定样式 */
        .pain-point-section {
            width: 100%;
            margin: 15px 0;
        }
        
        .pain-point-section .icon {
            font-size: 40px;
            margin-bottom: 10px;
        }

        .pain-point-section .body-text {
            font-size: 14px;
            color: rgba(255,255,255,0.8);
        }
        
        .connection-symbol {
            font-size: 80px;
            font-weight: 700;
            margin: 30px 0;
        }

        .dialogue-box {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 25px;
            width: 90%;
            margin-top: 20px;
        }
        
        .dialogue-box .body-text {
            font-weight: 700;
        }

        /* 主题渐变色库 */
        .page-1 { background: linear-gradient(135deg, #a29bfe, #6c5ce7); } /* 午夜紫 */
        .page-2 { background: linear-gradient(135deg, #48c6ef, #6f86d6); } /* 深海蓝 */
        .page-3 { background: linear-gradient(135deg, #636e72, #2d3436); } /* 石墨灰 */
        .page-4 { background: linear-gradient(135deg, #e0c3fc, #8ec5fc); } /* 梦幻紫 */
        .page-5 { background: linear-gradient(135deg, #00cec9, #55a3ff); } /* 清新青 */

    </style>
</head>
<body>

    <!-- 卡片1: 封面卡 -->
    <div class="card page-1">
        <div class="main-emoji">🐑</div>
        <div class="title">万物复刻史祖</div>
        <div class="body-text">暨「复制粘贴」行为总冠名</div>
        <div class="subtitle" style="margin-top: 40px;">1996.07.05</div>
    </div>

    <!-- 卡片2: 历史背景卡 -->
    <div class="card page-2">
        <div class="history-header">历史上的今天</div>
        <div class="icon">🔬</div>
        <div class="title">一只羊的诞生</div>
        <div class="body-text">震惊了世界<br>因为它没有父亲，只有三个妈</div>
        <div class="subtitle" style="margin-top: 30px;">多莉 (Dolly)</div>
        <div class="body-text">全球首例从成年体细胞克隆的哺乳动物</div>
    </div>

    <!-- 卡片3: 痛点卡 -->
    <div class="card page-3">
        <div class="title">当代社畜三大“原创”难题</div>
        <div class="pain-point-section">
            <div class="icon">📄</div>
            <div class="subtitle" style="font-size: 20px;">学术/职场</div>
            <div class="body-text">论文 | 方案 | 周报</div>
        </div>
        <div class="pain-point-section">
            <div class="icon">📱</div>
            <div class="subtitle" style="font-size: 20px;">赛博生活</div>
            <div class="body-text">爆款文案 | 网红人设 | Vlog模板</div>
        </div>
        <div class="pain-point-section">
            <div class="icon">🎮</div>
            <div class="subtitle" style="font-size: 20px;">精神世界</div>
            <div class="body-text">游戏角色 | 最强出装 | 通关攻略</div>
        </div>
    </div>

    <!-- 卡片4: 关联卡 -->
    <div class="card page-4">
        <div class="title">所以，别挣扎了</div>
        <div class="body-text">在你学会使用快捷键之前...</div>
        <div style="display: flex; align-items: center; justify-content: space-around; width: 100%; margin: 20px 0;">
            <div class="main-emoji">🐑</div>
            <div class="connection-symbol">≈</div>
            <div class="title" style="font-family: monospace;">Ctrl+C<br>+<br>Ctrl+V</div>
        </div>
        <div class="body-text">...科学已经帮你完成了生物学上的预演</div>
        <div class="subtitle" style="margin-top: 30px;">她是人类迷惑行为的<br>生物学圣母</div>
    </div>

    <!-- 卡片5: 用法卡 -->
    <div class="card page-5">
        <div class="title">“社交武器”已配发</div>
        <div class="body-text">当有人质疑你的原创性时<br>请打开此对话框：</div>
        <div class="dialogue-box">
            <div class="body-text" style="font-size: 18px;">
                “我没抄袭，<br>我这是在向克隆羊多莉的<br>科学探索精神致敬。”
            </div>
        </div>
         <div class="body-text" style="margin-top: 40px;">#今天你致敬了吗</div>
    </div>

</body>
</html>
