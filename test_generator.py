#!/usr/bin/env python3
"""
Gemini内容生成器测试脚本
用于测试程序的基本功能
"""

import os
import json
import tempfile
import shutil
from generate_gemini_content import GeminiContentGenerator

def test_config_loading():
    """测试配置文件加载"""
    print("🧪 测试配置文件加载...")
    
    # 创建临时配置文件
    test_config = {
        "api_keys": ["test_key_1", "test_key_2"],
        "prompt_path": "prompt.md",
        "start_date": "2025-07-05",
        "end_date": "2025-07-06",
        "versions_per_date": 2,
        "max_retries": 2,
        "retry_delay": 1,
        "request_delay": 1
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(test_config, f, ensure_ascii=False, indent=2)
        config_file = f.name
    
    try:
        generator = GeminiContentGenerator(config_file)
        
        # 验证配置加载
        assert generator.config['api_keys'] == ["test_key_1", "test_key_2"]
        assert generator.config['start_date'] == "2025-07-05"
        assert generator.config['end_date'] == "2025-07-06"
        
        print("✅ 配置文件加载测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载测试失败: {e}")
        return False
    finally:
        os.unlink(config_file)

def test_date_range_generation():
    """测试日期范围生成"""
    print("🧪 测试日期范围生成...")
    
    try:
        # 创建临时配置
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({"api_keys": ["test"]}, f)
            config_file = f.name
        
        generator = GeminiContentGenerator(config_file)
        
        # 测试日期范围生成
        dates = generator.generate_date_range("2025-07-05", "2025-07-07")
        expected = ["20250705", "20250706", "20250707"]
        
        assert dates == expected, f"期望 {expected}, 实际 {dates}"
        
        print("✅ 日期范围生成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 日期范围生成测试失败: {e}")
        return False
    finally:
        os.unlink(config_file)

def test_content_parsing():
    """测试内容解析"""
    print("🧪 测试AI响应解析...")
    
    try:
        # 创建临时配置
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({"api_keys": ["test"]}, f)
            config_file = f.name
        
        generator = GeminiContentGenerator(config_file)
        
        # 测试标准格式解析
        test_response = """
        [HTML]
        <!DOCTYPE html>
        <html>
        <head><title>Test</title></head>
        <body><h1>测试内容</h1></body>
        </html>
        [TXT]
        这是测试视频文案内容
        """
        
        html, txt = generator.parse_ai_response(test_response)
        
        assert "<!DOCTYPE html>" in html
        assert "测试内容" in html
        assert "测试视频文案内容" in txt
        
        print("✅ AI响应解析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ AI响应解析测试失败: {e}")
        return False
    finally:
        os.unlink(config_file)

def test_file_operations():
    """测试文件操作"""
    print("🧪 测试文件保存和检查...")
    
    try:
        # 创建临时配置
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({"api_keys": ["test"]}, f)
            config_file = f.name
        
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp()
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        generator = GeminiContentGenerator(config_file)
        
        # 测试内容保存
        test_html = "<html><body><h1>测试</h1></body></html>"
        test_txt = "测试文案内容"
        
        success = generator.save_content("20250705", 1, test_html, test_txt)
        assert success, "内容保存失败"
        
        # 验证文件是否创建
        assert os.path.exists("20250705/1.html"), "HTML文件未创建"
        assert os.path.exists("20250705/1.txt"), "TXT文件未创建"
        
        # 测试内容检查
        exists = generator.check_existing_content("20250705", 1)
        assert exists, "内容检查失败"
        
        # 验证文件内容
        with open("20250705/1.html", 'r', encoding='utf-8') as f:
            saved_html = f.read()
            assert saved_html == test_html, "HTML内容不匹配"
        
        with open("20250705/1.txt", 'r', encoding='utf-8') as f:
            saved_txt = f.read()
            assert saved_txt == test_txt, "TXT内容不匹配"
        
        print("✅ 文件操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)
        shutil.rmtree(temp_dir, ignore_errors=True)
        os.unlink(config_file)

def test_progress_management():
    """测试进度管理"""
    print("🧪 测试进度保存和加载...")
    
    try:
        # 创建临时配置
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({"api_keys": ["test"]}, f)
            config_file = f.name
        
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp()
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        generator = GeminiContentGenerator(config_file)
        
        # 测试进度保存
        test_tasks = [
            {"date": "20250705", "version": 1, "timestamp": "2025-07-01T10:00:00"},
            {"date": "20250705", "version": 2, "timestamp": "2025-07-01T10:05:00"}
        ]
        
        generator.save_progress(test_tasks)
        
        # 验证进度文件是否创建
        assert os.path.exists("progress.json"), "进度文件未创建"
        
        # 测试进度加载
        loaded_tasks = generator.load_progress()
        assert len(loaded_tasks) == 2, f"期望加载2个任务，实际加载{len(loaded_tasks)}个"
        assert loaded_tasks[0]["date"] == "20250705", "任务数据不匹配"
        
        print("✅ 进度管理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 进度管理测试失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)
        shutil.rmtree(temp_dir, ignore_errors=True)
        os.unlink(config_file)

def main():
    """运行所有测试"""
    print("🚀 Gemini内容生成器 - 功能测试")
    print("=" * 50)
    
    tests = [
        test_config_loading,
        test_date_range_generation,
        test_content_parsing,
        test_file_operations,
        test_progress_management
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序基本功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查程序代码")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
